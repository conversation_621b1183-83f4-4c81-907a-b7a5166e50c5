<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\HikingRoute;
use App\Models\RouteCategory;
use App\Http\Requests\StoreRouteRequest;
use App\Http\Requests\UpdateRouteRequest;
use App\Http\Resources\RouteResource;
use App\Http\Resources\RouteDetailResource;
use App\Services\RouteService;
use App\Services\SafetyService;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;

class RouteController extends Controller
{
    protected RouteService $routeService;
    protected SafetyService $safetyService;

    public function __construct(RouteService $routeService, SafetyService $safetyService)
    {
        $this->routeService = $routeService;
        $this->safetyService = $safetyService;
        
        $this->middleware('auth:sanctum')->except(['index', 'show', 'nearby', 'categories']);
        $this->middleware('verified')->only(['store', 'update', 'destroy']);
    }

    /**
     * 获取路线列表
     */
    public function index(Request $request): JsonResponse
    {
        $query = HikingRoute::with(['category', 'creator'])
            ->public()
            ->verified();

        // 搜索
        if ($request->filled('search')) {
            $query->where(function ($q) use ($request) {
                $q->where('name', 'like', '%' . $request->search . '%')
                  ->orWhere('description', 'like', '%' . $request->search . '%')
                  ->orWhere('detailed_location', 'like', '%' . $request->search . '%');
            });
        }

        // 分类筛选
        if ($request->filled('category_id')) {
            $query->byCategory($request->category_id);
        }

        // 难度筛选
        if ($request->filled('difficulty')) {
            $query->byDifficulty($request->difficulty);
        }

        // 地区筛选
        if ($request->filled('province')) {
            $query->where('province', $request->province);
        }
        if ($request->filled('city')) {
            $query->where('city', $request->city);
        }

        // 距离筛选
        if ($request->filled('distance_min')) {
            $query->where('distance', '>=', $request->distance_min);
        }
        if ($request->filled('distance_max')) {
            $query->where('distance', '<=', $request->distance_max);
        }

        // 海拔筛选
        if ($request->filled('elevation_min')) {
            $query->where('max_elevation', '>=', $request->elevation_min);
        }
        if ($request->filled('elevation_max')) {
            $query->where('max_elevation', '<=', $request->elevation_max);
        }

        // 附近路线
        if ($request->filled(['latitude', 'longitude'])) {
            $radius = $request->get('radius', 50); // 默认50公里
            $query->nearby($request->latitude, $request->longitude, $radius);
        }

        // 排序
        $sortBy = $request->get('sort_by', 'created_at');
        $sortOrder = $request->get('sort_order', 'desc');
        
        $allowedSorts = ['created_at', 'average_rating', 'view_count', 'completion_count', 'distance', 'difficulty_score'];
        if (in_array($sortBy, $allowedSorts)) {
            $query->orderBy($sortBy, $sortOrder);
        }

        $routes = $query->paginate($request->get('per_page', 15));

        return response()->json([
            'success' => true,
            'data' => RouteResource::collection($routes),
            'meta' => [
                'current_page' => $routes->currentPage(),
                'last_page' => $routes->lastPage(),
                'per_page' => $routes->perPage(),
                'total' => $routes->total(),
            ],
        ]);
    }

    /**
     * 获取路线详情
     */
    public function show(HikingRoute $route): JsonResponse
    {
        if (!$route->isAccessible()) {
            return response()->json([
                'success' => false,
                'message' => '该路线暂不可访问',
            ], 403);
        }

        $route->load([
            'category',
            'creator',
            'points',
            'reviews.user',
            'photos',
            'weatherAlerts' => function ($query) {
                $query->where('status', 'active')
                      ->where('start_time', '<=', now())
                      ->where('end_time', '>=', now());
            },
            'geologicalRisks' => function ($query) {
                $query->where('status', 'active');
            },
        ]);

        // 增加浏览次数
        $route->incrementViewCount();

        // 获取安全状态
        $safetyStatus = $this->safetyService->getRouteSafetyStatus($route);

        return response()->json([
            'success' => true,
            'data' => new RouteDetailResource($route),
            'safety_status' => $safetyStatus,
        ]);
    }

    /**
     * 创建新路线
     */
    public function store(StoreRouteRequest $request): JsonResponse
    {
        if (!auth()->user()->canCreateRoute()) {
            return response()->json([
                'success' => false,
                'message' => '您暂无权限创建路线',
            ], 403);
        }

        try {
            $route = $this->routeService->createRoute($request->validated(), auth()->user());

            return response()->json([
                'success' => true,
                'message' => '路线创建成功',
                'data' => new RouteDetailResource($route),
            ], 201);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => '路线创建失败：' . $e->getMessage(),
            ], 500);
        }
    }

    /**
     * 更新路线
     */
    public function update(UpdateRouteRequest $request, HikingRoute $route): JsonResponse
    {
        if (!$this->canModifyRoute($route)) {
            return response()->json([
                'success' => false,
                'message' => '您无权修改此路线',
            ], 403);
        }

        try {
            $route = $this->routeService->updateRoute($route, $request->validated());

            return response()->json([
                'success' => true,
                'message' => '路线更新成功',
                'data' => new RouteDetailResource($route),
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => '路线更新失败：' . $e->getMessage(),
            ], 500);
        }
    }

    /**
     * 删除路线
     */
    public function destroy(HikingRoute $route): JsonResponse
    {
        if (!$this->canModifyRoute($route)) {
            return response()->json([
                'success' => false,
                'message' => '您无权删除此路线',
            ], 403);
        }

        try {
            $route->delete();

            return response()->json([
                'success' => true,
                'message' => '路线删除成功',
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => '路线删除失败：' . $e->getMessage(),
            ], 500);
        }
    }

    /**
     * 获取附近路线
     */
    public function nearby(Request $request): JsonResponse
    {
        $request->validate([
            'latitude' => 'required|numeric|between:-90,90',
            'longitude' => 'required|numeric|between:-180,180',
            'radius' => 'nullable|numeric|min:1|max:200',
        ]);

        $routes = HikingRoute::with(['category', 'creator'])
            ->public()
            ->verified()
            ->nearby(
                $request->latitude,
                $request->longitude,
                $request->get('radius', 50)
            )
            ->orderBy('average_rating', 'desc')
            ->limit(20)
            ->get();

        return response()->json([
            'success' => true,
            'data' => RouteResource::collection($routes),
        ]);
    }

    /**
     * 获取路线分类
     */
    public function categories(): JsonResponse
    {
        $categories = RouteCategory::where('is_active', true)
            ->orderBy('sort_order')
            ->get();

        return response()->json([
            'success' => true,
            'data' => $categories,
        ]);
    }

    /**
     * 收藏/取消收藏路线
     */
    public function toggleFavorite(HikingRoute $route): JsonResponse
    {
        $user = auth()->user();
        
        if ($user->favoriteRoutes()->where('hiking_route_id', $route->id)->exists()) {
            $user->favoriteRoutes()->detach($route->id);
            $route->decrement('favorite_count');
            $message = '已取消收藏';
            $favorited = false;
        } else {
            $user->favoriteRoutes()->attach($route->id);
            $route->increment('favorite_count');
            $message = '收藏成功';
            $favorited = true;
        }

        return response()->json([
            'success' => true,
            'message' => $message,
            'favorited' => $favorited,
        ]);
    }

    /**
     * 检查是否可以修改路线
     */
    private function canModifyRoute(HikingRoute $route): bool
    {
        $user = auth()->user();
        
        return $user->hasRole('admin') || 
               $route->created_by === $user->id ||
               ($user->hasRole('moderator') && $route->status !== 'approved');
    }
}
