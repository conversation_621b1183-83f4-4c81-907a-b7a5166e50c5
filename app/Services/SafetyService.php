<?php

namespace App\Services;

use App\Models\HikingRoute;
use App\Models\WeatherAlert;
use App\Models\GeologicalRisk;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Cache;
use Carbon\Carbon;

class SafetyService
{
    /**
     * 获取路线安全状态
     */
    public function getRouteSafetyStatus(HikingRoute $route): array
    {
        $weatherAlerts = $this->getActiveWeatherAlerts($route);
        $geologicalRisks = $this->getActiveGeologicalRisks($route);
        $currentWeather = $this->getCurrentWeather($route);
        
        $overallRisk = $this->calculateOverallRisk($weatherAlerts, $geologicalRisks);
        
        return [
            'overall_risk' => $overallRisk,
            'risk_level' => $this->getRiskLevelName($overallRisk),
            'weather_alerts' => $weatherAlerts,
            'geological_risks' => $geologicalRisks,
            'current_weather' => $currentWeather,
            'recommendations' => $this->generateRecommendations($overallRisk, $weatherAlerts, $geologicalRisks),
            'last_updated' => now()->toISOString(),
        ];
    }

    /**
     * 获取活跃的天气预警
     */
    private function getActiveWeatherAlerts(HikingRoute $route): array
    {
        $alerts = WeatherAlert::where('route_id', $route->id)
            ->where('status', 'active')
            ->where('start_time', '<=', now())
            ->where('end_time', '>=', now())
            ->orderBy('severity', 'desc')
            ->get();

        return $alerts->map(function ($alert) {
            return [
                'id' => $alert->id,
                'type' => $alert->alert_type,
                'severity' => $alert->severity,
                'title' => $alert->title,
                'description' => $alert->description,
                'recommendations' => $alert->recommendations,
                'start_time' => $alert->start_time->toISOString(),
                'end_time' => $alert->end_time->toISOString(),
                'weather_data' => $alert->weather_data,
            ];
        })->toArray();
    }

    /**
     * 获取活跃的地质风险
     */
    private function getActiveGeologicalRisks(HikingRoute $route): array
    {
        $risks = GeologicalRisk::where('route_id', $route->id)
            ->where('status', 'active')
            ->orderBy('risk_level', 'desc')
            ->get();

        return $risks->map(function ($risk) {
            return [
                'id' => $risk->id,
                'type' => $risk->risk_type,
                'level' => $risk->risk_level,
                'title' => $risk->title,
                'description' => $risk->description,
                'safety_measures' => $risk->safety_measures,
                'alternative_routes' => $risk->alternative_routes,
                'latitude' => $risk->latitude,
                'longitude' => $risk->longitude,
                'radius' => $risk->radius,
                'seasonal_factors' => $risk->seasonal_factors,
                'last_assessment_date' => $risk->last_assessment_date?->toISOString(),
            ];
        })->toArray();
    }

    /**
     * 获取当前天气
     */
    private function getCurrentWeather(HikingRoute $route): ?array
    {
        $cacheKey = "weather_current_{$route->id}";
        
        return Cache::remember($cacheKey, 600, function () use ($route) { // 缓存10分钟
            try {
                $apiKey = config('services.weather.api_key');
                if (!$apiKey) {
                    return null;
                }

                $response = Http::get('https://api.openweathermap.org/data/2.5/weather', [
                    'lat' => $route->start_latitude,
                    'lon' => $route->start_longitude,
                    'appid' => $apiKey,
                    'units' => 'metric',
                    'lang' => 'zh_cn',
                ]);

                if ($response->successful()) {
                    $data = $response->json();
                    
                    return [
                        'temperature' => $data['main']['temp'],
                        'feels_like' => $data['main']['feels_like'],
                        'humidity' => $data['main']['humidity'],
                        'pressure' => $data['main']['pressure'],
                        'visibility' => isset($data['visibility']) ? $data['visibility'] / 1000 : null, // 转换为公里
                        'wind_speed' => $data['wind']['speed'] ?? 0,
                        'wind_direction' => $data['wind']['deg'] ?? 0,
                        'weather' => [
                            'main' => $data['weather'][0]['main'],
                            'description' => $data['weather'][0]['description'],
                            'icon' => $data['weather'][0]['icon'],
                        ],
                        'clouds' => $data['clouds']['all'] ?? 0,
                        'uv_index' => null, // 需要额外API调用
                        'sunrise' => Carbon::createFromTimestamp($data['sys']['sunrise'])->toISOString(),
                        'sunset' => Carbon::createFromTimestamp($data['sys']['sunset'])->toISOString(),
                    ];
                }
            } catch (\Exception $e) {
                \Log::error('获取天气数据失败: ' . $e->getMessage());
            }

            return null;
        });
    }

    /**
     * 计算总体风险等级
     */
    private function calculateOverallRisk(array $weatherAlerts, array $geologicalRisks): int
    {
        $riskScore = 0;

        // 天气预警风险评分
        foreach ($weatherAlerts as $alert) {
            switch ($alert['severity']) {
                case 'extreme':
                    $riskScore += 4;
                    break;
                case 'high':
                    $riskScore += 3;
                    break;
                case 'moderate':
                    $riskScore += 2;
                    break;
                case 'low':
                    $riskScore += 1;
                    break;
            }
        }

        // 地质风险评分
        foreach ($geologicalRisks as $risk) {
            switch ($risk['level']) {
                case 'extreme':
                    $riskScore += 4;
                    break;
                case 'high':
                    $riskScore += 3;
                    break;
                case 'moderate':
                    $riskScore += 2;
                    break;
                case 'low':
                    $riskScore += 1;
                    break;
            }
        }

        // 将评分转换为1-5的风险等级
        if ($riskScore >= 8) {
            return 5; // 极高风险
        } elseif ($riskScore >= 6) {
            return 4; // 高风险
        } elseif ($riskScore >= 4) {
            return 3; // 中等风险
        } elseif ($riskScore >= 2) {
            return 2; // 低风险
        } elseif ($riskScore >= 1) {
            return 1; // 很低风险
        }

        return 0; // 无明显风险
    }

    /**
     * 获取风险等级名称
     */
    private function getRiskLevelName(int $riskLevel): string
    {
        $levels = [
            0 => '安全',
            1 => '很低风险',
            2 => '低风险',
            3 => '中等风险',
            4 => '高风险',
            5 => '极高风险',
        ];

        return $levels[$riskLevel] ?? '未知';
    }

    /**
     * 生成安全建议
     */
    private function generateRecommendations(int $overallRisk, array $weatherAlerts, array $geologicalRisks): array
    {
        $recommendations = [];

        // 基于总体风险等级的建议
        switch ($overallRisk) {
            case 5:
                $recommendations[] = '⚠️ 强烈建议取消或推迟此次徒步活动';
                $recommendations[] = '🚫 当前条件极其危险，不适合任何户外活动';
                break;
            case 4:
                $recommendations[] = '⚠️ 建议推迟徒步活动';
                $recommendations[] = '👥 如必须前往，请确保有经验丰富的领队陪同';
                $recommendations[] = '📱 保持通讯设备畅通，准备应急撤退计划';
                break;
            case 3:
                $recommendations[] = '⚠️ 请谨慎评估个人能力和装备';
                $recommendations[] = '👥 建议结伴而行，避免单独行动';
                $recommendations[] = '🎒 携带充足的安全装备和应急用品';
                break;
            case 2:
                $recommendations[] = '✅ 可以进行徒步，但需注意安全';
                $recommendations[] = '🎒 携带基本安全装备';
                break;
            case 1:
                $recommendations[] = '✅ 条件良好，适合徒步';
                $recommendations[] = '🌟 享受您的户外时光！';
                break;
            case 0:
                $recommendations[] = '✅ 条件优秀，非常适合徒步';
                break;
        }

        // 基于具体天气预警的建议
        foreach ($weatherAlerts as $alert) {
            switch ($alert['type']) {
                case 'heavy_rain':
                case 'thunderstorm':
                    $recommendations[] = '☔ 携带防雨装备，注意防雷安全';
                    break;
                case 'snow':
                case 'blizzard':
                    $recommendations[] = '❄️ 携带防寒装备和防滑设备';
                    break;
                case 'high_wind':
                    $recommendations[] = '💨 注意大风天气，避免在悬崖边缘活动';
                    break;
                case 'extreme_heat':
                    $recommendations[] = '🌡️ 携带充足水源，避免中暑';
                    break;
                case 'fog':
                    $recommendations[] = '🌫️ 能见度低，注意导航，建议使用GPS设备';
                    break;
            }
        }

        // 基于地质风险的建议
        foreach ($geologicalRisks as $risk) {
            switch ($risk['type']) {
                case 'rockfall':
                case 'landslide':
                    $recommendations[] = '⛑️ 佩戴安全头盔，快速通过危险区域';
                    break;
                case 'avalanche':
                    $recommendations[] = '🎿 携带雪崩安全装备，了解雪崩逃生知识';
                    break;
                case 'river_crossing':
                    $recommendations[] = '🌊 注意河流水位，必要时寻找替代路线';
                    break;
                case 'cliff_edge':
                    $recommendations[] = '🧗 远离悬崖边缘，使用安全绳索';
                    break;
            }
        }

        return array_unique($recommendations);
    }

    /**
     * 创建天气预警
     */
    public function createWeatherAlert(array $data): WeatherAlert
    {
        return WeatherAlert::create($data);
    }

    /**
     * 更新天气数据
     */
    public function updateWeatherData(HikingRoute $route): void
    {
        try {
            // 获取未来几天的天气预报
            $forecast = $this->getWeatherForecast($route);
            
            if ($forecast) {
                $this->processWeatherForecast($route, $forecast);
            }
        } catch (\Exception $e) {
            \Log::error('更新天气数据失败: ' . $e->getMessage());
        }
    }

    /**
     * 获取天气预报
     */
    private function getWeatherForecast(HikingRoute $route): ?array
    {
        try {
            $apiKey = config('services.weather.api_key');
            if (!$apiKey) {
                return null;
            }

            $response = Http::get('https://api.openweathermap.org/data/2.5/forecast', [
                'lat' => $route->start_latitude,
                'lon' => $route->start_longitude,
                'appid' => $apiKey,
                'units' => 'metric',
                'lang' => 'zh_cn',
            ]);

            return $response->successful() ? $response->json() : null;
        } catch (\Exception $e) {
            \Log::error('获取天气预报失败: ' . $e->getMessage());
            return null;
        }
    }

    /**
     * 处理天气预报数据
     */
    private function processWeatherForecast(HikingRoute $route, array $forecast): void
    {
        foreach ($forecast['list'] as $item) {
            $datetime = Carbon::createFromTimestamp($item['dt']);
            $weather = $item['weather'][0];
            
            // 检查是否需要创建预警
            $alertData = $this->analyzeWeatherForAlert($route, $item, $datetime);
            
            if ($alertData) {
                // 检查是否已存在相同的预警
                $existingAlert = WeatherAlert::where('route_id', $route->id)
                    ->where('alert_type', $alertData['alert_type'])
                    ->where('start_time', '<=', $alertData['start_time'])
                    ->where('end_time', '>=', $alertData['end_time'])
                    ->first();
                
                if (!$existingAlert) {
                    $this->createWeatherAlert($alertData);
                }
            }
        }
    }

    /**
     * 分析天气数据是否需要创建预警
     */
    private function analyzeWeatherForAlert(HikingRoute $route, array $weatherData, Carbon $datetime): ?array
    {
        $weather = $weatherData['weather'][0];
        $main = $weatherData['main'];
        $wind = $weatherData['wind'] ?? [];
        
        $alertType = null;
        $severity = 'low';
        $title = '';
        $description = '';
        
        // 分析不同类型的天气风险
        if (in_array($weather['main'], ['Thunderstorm'])) {
            $alertType = 'thunderstorm';
            $severity = 'high';
            $title = '雷暴预警';
            $description = '预计将有雷暴天气，请注意安全';
        } elseif (in_array($weather['main'], ['Rain']) && ($weatherData['rain']['3h'] ?? 0) > 10) {
            $alertType = 'heavy_rain';
            $severity = ($weatherData['rain']['3h'] ?? 0) > 25 ? 'high' : 'moderate';
            $title = '强降雨预警';
            $description = '预计将有强降雨，注意防滑和山洪风险';
        } elseif (in_array($weather['main'], ['Snow'])) {
            $alertType = 'snow';
            $severity = ($weatherData['snow']['3h'] ?? 0) > 5 ? 'high' : 'moderate';
            $title = '降雪预警';
            $description = '预计将有降雪，注意保暖和防滑';
        } elseif (($wind['speed'] ?? 0) > 10) {
            $alertType = 'high_wind';
            $severity = ($wind['speed'] ?? 0) > 15 ? 'high' : 'moderate';
            $title = '大风预警';
            $description = '预计将有大风天气，注意安全';
        } elseif ($main['temp'] > 35) {
            $alertType = 'extreme_heat';
            $severity = $main['temp'] > 40 ? 'extreme' : 'high';
            $title = '高温预警';
            $description = '预计将有高温天气，注意防暑降温';
        } elseif ($main['temp'] < -10) {
            $alertType = 'extreme_cold';
            $severity = $main['temp'] < -20 ? 'extreme' : 'high';
            $title = '严寒预警';
            $description = '预计将有严寒天气，注意保暖';
        }
        
        if ($alertType) {
            return [
                'route_id' => $route->id,
                'alert_type' => $alertType,
                'severity' => $severity,
                'title' => $title,
                'description' => $description,
                'start_time' => $datetime,
                'end_time' => $datetime->copy()->addHours(3),
                'issued_at' => now(),
                'weather_data' => $weatherData,
                'source' => 'openweather',
                'is_automatic' => true,
            ];
        }
        
        return null;
    }
}
