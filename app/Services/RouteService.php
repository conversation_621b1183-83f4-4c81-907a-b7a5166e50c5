<?php

namespace App\Services;

use App\Models\HikingRoute;
use App\Models\User;
use App\Models\RoutePoint;
use Illuminate\Support\Str;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Storage;

class RouteService
{
    /**
     * 创建新路线
     */
    public function createRoute(array $data, User $user): HikingRoute
    {
        return DB::transaction(function () use ($data, $user) {
            // 生成唯一的slug
            $data['slug'] = $this->generateUniqueSlug($data['name']);
            $data['created_by'] = $user->id;
            
            // 计算难度评分
            $data['difficulty_score'] = $this->calculateDifficultyScore($data);
            
            // 创建路线
            $route = HikingRoute::create($data);
            
            // 处理GPX文件
            if (isset($data['gpx_file'])) {
                $this->processGpxFile($route, $data['gpx_file']);
            }
            
            // 创建路线关键点
            if (isset($data['points']) && is_array($data['points'])) {
                $this->createRoutePoints($route, $data['points']);
            }
            
            // 处理路线图片
            if (isset($data['images']) && is_array($data['images'])) {
                $this->processRouteImages($route, $data['images']);
            }
            
            // 给用户增加积分
            $user->addPoints(50, '创建路线：' . $route->name);
            
            return $route->load(['category', 'creator', 'points']);
        });
    }

    /**
     * 更新路线
     */
    public function updateRoute(HikingRoute $route, array $data): HikingRoute
    {
        return DB::transaction(function () use ($route, $data) {
            // 如果名称改变，重新生成slug
            if (isset($data['name']) && $data['name'] !== $route->name) {
                $data['slug'] = $this->generateUniqueSlug($data['name'], $route->id);
            }
            
            // 重新计算难度评分
            if ($this->shouldRecalculateDifficulty($data)) {
                $data['difficulty_score'] = $this->calculateDifficultyScore(array_merge($route->toArray(), $data));
            }
            
            // 如果路线被修改，重置验证状态
            if ($this->isSignificantChange($route, $data)) {
                $data['verification_status'] = 'unverified';
                $data['verified_by'] = null;
                $data['verified_at'] = null;
            }
            
            $route->update($data);
            
            // 更新路线关键点
            if (isset($data['points']) && is_array($data['points'])) {
                $this->updateRoutePoints($route, $data['points']);
            }
            
            // 处理新的GPX文件
            if (isset($data['gpx_file'])) {
                $this->processGpxFile($route, $data['gpx_file']);
            }
            
            return $route->fresh(['category', 'creator', 'points']);
        });
    }

    /**
     * 生成唯一的slug
     */
    private function generateUniqueSlug(string $name, ?int $excludeId = null): string
    {
        $baseSlug = Str::slug($name);
        $slug = $baseSlug;
        $counter = 1;
        
        while (true) {
            $query = HikingRoute::where('slug', $slug);
            if ($excludeId) {
                $query->where('id', '!=', $excludeId);
            }
            
            if (!$query->exists()) {
                break;
            }
            
            $slug = $baseSlug . '-' . $counter;
            $counter++;
        }
        
        return $slug;
    }

    /**
     * 计算路线难度评分
     */
    private function calculateDifficultyScore(array $data): float
    {
        $score = 0;
        
        // 距离因子 (0-3分)
        $distance = $data['distance'] ?? 0;
        if ($distance <= 5) {
            $score += 0.5;
        } elseif ($distance <= 15) {
            $score += 1.0;
        } elseif ($distance <= 30) {
            $score += 2.0;
        } else {
            $score += 3.0;
        }
        
        // 爬升因子 (0-3分)
        $elevation = $data['elevation_gain'] ?? 0;
        if ($elevation <= 300) {
            $score += 0.5;
        } elseif ($elevation <= 800) {
            $score += 1.0;
        } elseif ($elevation <= 1500) {
            $score += 2.0;
        } else {
            $score += 3.0;
        }
        
        // 海拔因子 (0-2分)
        $maxElevation = $data['max_elevation'] ?? 0;
        if ($maxElevation >= 3000) {
            $score += 2.0;
        } elseif ($maxElevation >= 2000) {
            $score += 1.0;
        } elseif ($maxElevation >= 1000) {
            $score += 0.5;
        }
        
        // 技术等级因子 (0-2分)
        $technicalGrade = $data['technical_grade'] ?? 'T1';
        $gradeScores = [
            'T1' => 0,
            'T2' => 0.3,
            'T3' => 0.7,
            'T4' => 1.2,
            'T5' => 1.7,
            'T6' => 2.0,
        ];
        $score += $gradeScores[$technicalGrade] ?? 0;
        
        return round($score, 1);
    }

    /**
     * 处理GPX文件
     */
    private function processGpxFile(HikingRoute $route, $gpxFile): void
    {
        // 保存GPX文件
        $path = $gpxFile->store('gpx/' . $route->id, 'public');
        $route->update(['gpx_file_path' => $path]);
        
        // 解析GPX文件获取轨迹点
        $trackPoints = $this->parseGpxFile($gpxFile);
        if (!empty($trackPoints)) {
            $route->update(['track_points' => $trackPoints]);
        }
    }

    /**
     * 解析GPX文件
     */
    private function parseGpxFile($gpxFile): array
    {
        try {
            $content = file_get_contents($gpxFile->getRealPath());
            $xml = simplexml_load_string($content);
            
            if (!$xml) {
                return [];
            }
            
            $trackPoints = [];
            
            // 解析轨迹点
            foreach ($xml->trk->trkseg->trkpt as $point) {
                $trackPoints[] = [
                    'lat' => (float) $point['lat'],
                    'lon' => (float) $point['lon'],
                    'ele' => isset($point->ele) ? (float) $point->ele : null,
                    'time' => isset($point->time) ? (string) $point->time : null,
                ];
            }
            
            return $trackPoints;
        } catch (\Exception $e) {
            \Log::error('GPX文件解析失败: ' . $e->getMessage());
            return [];
        }
    }

    /**
     * 创建路线关键点
     */
    private function createRoutePoints(HikingRoute $route, array $points): void
    {
        foreach ($points as $index => $pointData) {
            RoutePoint::create([
                'route_id' => $route->id,
                'name' => $pointData['name'],
                'type' => $pointData['type'],
                'latitude' => $pointData['latitude'],
                'longitude' => $pointData['longitude'],
                'elevation' => $pointData['elevation'] ?? null,
                'description' => $pointData['description'] ?? null,
                'notes' => $pointData['notes'] ?? null,
                'facilities' => $pointData['facilities'] ?? null,
                'services' => $pointData['services'] ?? null,
                'distance_from_start' => $pointData['distance_from_start'] ?? null,
                'estimated_time_from_start' => $pointData['estimated_time_from_start'] ?? null,
                'safety_level' => $pointData['safety_level'] ?? 'safe',
                'safety_notes' => $pointData['safety_notes'] ?? null,
                'has_mobile_signal' => $pointData['has_mobile_signal'] ?? false,
                'sort_order' => $index,
                'created_by' => $route->created_by,
            ]);
        }
    }

    /**
     * 更新路线关键点
     */
    private function updateRoutePoints(HikingRoute $route, array $points): void
    {
        // 删除现有关键点
        $route->points()->delete();
        
        // 创建新的关键点
        $this->createRoutePoints($route, $points);
    }

    /**
     * 处理路线图片
     */
    private function processRouteImages(HikingRoute $route, array $images): void
    {
        foreach ($images as $index => $image) {
            if ($index === 0) {
                // 第一张图片作为封面
                $route->addMediaFromRequest('images.0')
                      ->toMediaCollection('cover');
            } else {
                // 其他图片加入图片集
                $route->addMediaFromRequest("images.{$index}")
                      ->toMediaCollection('gallery');
            }
        }
    }

    /**
     * 检查是否需要重新计算难度
     */
    private function shouldRecalculateDifficulty(array $data): bool
    {
        $difficultyFields = [
            'distance', 'elevation_gain', 'max_elevation', 'technical_grade'
        ];
        
        foreach ($difficultyFields as $field) {
            if (isset($data[$field])) {
                return true;
            }
        }
        
        return false;
    }

    /**
     * 检查是否为重大修改
     */
    private function isSignificantChange(HikingRoute $route, array $data): bool
    {
        $significantFields = [
            'start_latitude', 'start_longitude', 'end_latitude', 'end_longitude',
            'distance', 'elevation_gain', 'difficulty_level', 'technical_grade',
            'track_points', 'waypoints'
        ];
        
        foreach ($significantFields as $field) {
            if (isset($data[$field]) && $data[$field] != $route->$field) {
                return true;
            }
        }
        
        return false;
    }

    /**
     * 验证路线
     */
    public function verifyRoute(HikingRoute $route, User $verifier, string $status, ?string $notes = null): HikingRoute
    {
        $route->update([
            'verification_status' => $status,
            'verified_by' => $verifier->id,
            'verified_at' => now(),
            'rejection_reason' => $status === 'rejected' ? $notes : null,
        ]);
        
        // 给路线创建者增加积分
        if ($status === 'expert_verified' || $status === 'official_verified') {
            $route->creator->addPoints(100, '路线验证通过：' . $route->name);
        }
        
        return $route;
    }

    /**
     * 获取推荐路线
     */
    public function getRecommendedRoutes(User $user, int $limit = 10): \Illuminate\Database\Eloquent\Collection
    {
        // 基于用户等级和偏好推荐路线
        $userLevel = $user->hiking_level;
        $preferences = $user->preferences ?? [];
        
        $query = HikingRoute::public()->verified();
        
        // 根据用户等级筛选难度
        switch ($userLevel) {
            case 'beginner':
                $query->whereIn('difficulty_level', ['easy', 'moderate']);
                break;
            case 'intermediate':
                $query->whereIn('difficulty_level', ['moderate', 'hard']);
                break;
            case 'advanced':
            case 'expert':
                $query->whereIn('difficulty_level', ['hard', 'extreme']);
                break;
        }
        
        // 根据用户偏好筛选
        if (isset($preferences['preferred_categories']) && !empty($preferences['preferred_categories'])) {
            $query->whereIn('category_id', $preferences['preferred_categories']);
        }
        
        if (isset($preferences['max_distance'])) {
            $query->where('distance', '<=', $preferences['max_distance']);
        }
        
        return $query->orderBy('average_rating', 'desc')
                    ->orderBy('view_count', 'desc')
                    ->limit($limit)
                    ->get();
    }
}
