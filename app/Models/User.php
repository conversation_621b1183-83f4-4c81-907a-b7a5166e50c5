<?php

namespace App\Models;

use Illuminate\Contracts\Auth\MustVerifyEmail;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Foundation\Auth\User as Authenticatable;
use Illuminate\Notifications\Notifiable;
use <PERSON><PERSON>\Sanctum\HasApiTokens;
use <PERSON><PERSON>\Permission\Traits\HasRoles;
use Spatie\MediaLibrary\HasMedia;
use Spatie\MediaLibrary\InteractsWithMedia;

class User extends Authenticatable implements MustVerifyEmail, HasMedia
{
    use HasApiTokens, HasFactory, Notifiable, HasRoles, InteractsWithMedia;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'username',
        'email',
        'password',
        'phone',
        'real_name',
        'gender',
        'birth_date',
        'bio',
        'hiking_level',
        'hiking_years',
        'certifications',
        'is_guide',
        'guide_rating',
        'points',
        'level',
        'badges',
        'emergency_contact_enabled',
        'emergency_contacts',
        'location_sharing_enabled',
        'preferences',
        'custom_dashboard',
        'status',
        'last_active_at',
    ];

    /**
     * The attributes that should be hidden for serialization.
     *
     * @var array<int, string>
     */
    protected $hidden = [
        'password',
        'remember_token',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'email_verified_at' => 'datetime',
        'birth_date' => 'date',
        'certifications' => 'array',
        'badges' => 'array',
        'emergency_contacts' => 'array',
        'preferences' => 'array',
        'custom_dashboard' => 'array',
        'last_active_at' => 'datetime',
        'emergency_contact_enabled' => 'boolean',
        'location_sharing_enabled' => 'boolean',
        'is_guide' => 'boolean',
        'guide_rating' => 'decimal:2',
        'points' => 'integer',
        'hiking_years' => 'integer',
    ];

    /**
     * 用户创建的路线
     */
    public function createdRoutes()
    {
        return $this->hasMany(HikingRoute::class, 'created_by');
    }

    /**
     * 用户验证的路线
     */
    public function verifiedRoutes()
    {
        return $this->hasMany(HikingRoute::class, 'verified_by');
    }

    /**
     * 用户的路线评价
     */
    public function routeReviews()
    {
        return $this->hasMany(RouteReview::class);
    }

    /**
     * 用户收藏的路线
     */
    public function favoriteRoutes()
    {
        return $this->belongsToMany(HikingRoute::class, 'route_favorites')
                    ->withTimestamps();
    }

    /**
     * 用户的活动记录
     */
    public function activities()
    {
        return $this->hasMany(UserActivity::class);
    }

    /**
     * 用户创建的群组
     */
    public function createdGroups()
    {
        return $this->hasMany(Group::class, 'created_by');
    }

    /**
     * 用户加入的群组
     */
    public function groups()
    {
        return $this->belongsToMany(Group::class, 'group_members')
                    ->withPivot(['role', 'joined_at', 'status'])
                    ->withTimestamps();
    }

    /**
     * 用户上传的照片
     */
    public function photos()
    {
        return $this->hasMany(RoutePhoto::class, 'uploaded_by');
    }

    /**
     * 获取用户头像URL
     */
    public function getAvatarUrlAttribute()
    {
        if ($this->avatar) {
            return asset('storage/' . $this->avatar);
        }
        
        return $this->getFirstMediaUrl('avatars') ?: asset('images/default-avatar.png');
    }

    /**
     * 获取用户等级信息
     */
    public function getLevelInfoAttribute()
    {
        $levels = [
            0 => ['name' => '新手', 'min_points' => 0],
            1 => ['name' => '初级', 'min_points' => 100],
            2 => ['name' => '中级', 'min_points' => 500],
            3 => ['name' => '高级', 'min_points' => 1500],
            4 => ['name' => '专家', 'min_points' => 5000],
            5 => ['name' => '大师', 'min_points' => 15000],
        ];

        foreach (array_reverse($levels, true) as $level => $info) {
            if ($this->points >= $info['min_points']) {
                return array_merge($info, ['level' => $level]);
            }
        }

        return $levels[0];
    }

    /**
     * 检查用户是否可以创建路线
     */
    public function canCreateRoute(): bool
    {
        return $this->status === 'active' && 
               ($this->hasRole('admin') || $this->points >= 50);
    }

    /**
     * 检查用户是否可以验证路线
     */
    public function canVerifyRoute(): bool
    {
        return $this->status === 'active' && 
               ($this->hasRole(['admin', 'expert']) || 
                ($this->is_guide && $this->guide_rating >= 4.0));
    }

    /**
     * 更新用户活跃时间
     */
    public function updateLastActive()
    {
        $this->update(['last_active_at' => now()]);
    }

    /**
     * 增加用户积分
     */
    public function addPoints(int $points, string $reason = '')
    {
        $this->increment('points', $points);
        
        // 记录积分变化
        $this->activities()->create([
            'type' => 'points_earned',
            'data' => [
                'points' => $points,
                'reason' => $reason,
                'total_points' => $this->fresh()->points,
            ],
        ]);
    }
}
