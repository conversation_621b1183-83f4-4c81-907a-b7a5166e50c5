<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Spatie\MediaLibrary\HasMedia;
use Spatie\MediaLibrary\InteractsWithMedia;
use Spatie\MediaLibrary\MediaCollections\Models\Media;

class HikingRoute extends Model implements HasMedia
{
    use HasFactory, SoftDeletes, InteractsWithMedia;

    protected $fillable = [
        'name',
        'slug',
        'description',
        'category_id',
        'created_by',
        'start_latitude',
        'start_longitude',
        'end_latitude',
        'end_longitude',
        'province',
        'city',
        'district',
        'detailed_location',
        'distance',
        'elevation_gain',
        'elevation_loss',
        'max_elevation',
        'min_elevation',
        'estimated_duration',
        'difficulty_level',
        'technical_grade',
        'difficulty_score',
        'best_seasons',
        'recommended_start_time',
        'latest_return_time',
        'gpx_file_path',
        'track_points',
        'waypoints',
        'risk_factors',
        'safety_notes',
        'requires_permit',
        'permit_info',
        'required_equipment',
        'recommended_equipment',
        'has_water_sources',
        'has_camping_sites',
        'supply_info',
        'status',
        'verification_status',
        'verified_by',
        'verified_at',
        'rejection_reason',
        'tags',
        'metadata',
        'is_featured',
        'is_public',
    ];

    protected $casts = [
        'start_latitude' => 'decimal:8',
        'start_longitude' => 'decimal:8',
        'end_latitude' => 'decimal:8',
        'end_longitude' => 'decimal:8',
        'distance' => 'decimal:2',
        'difficulty_score' => 'decimal:1',
        'recommended_start_time' => 'datetime:H:i',
        'latest_return_time' => 'datetime:H:i',
        'verified_at' => 'datetime',
        'best_seasons' => 'array',
        'track_points' => 'array',
        'waypoints' => 'array',
        'risk_factors' => 'array',
        'required_equipment' => 'array',
        'recommended_equipment' => 'array',
        'tags' => 'array',
        'metadata' => 'array',
        'requires_permit' => 'boolean',
        'has_water_sources' => 'boolean',
        'has_camping_sites' => 'boolean',
        'is_featured' => 'boolean',
        'is_public' => 'boolean',
        'elevation_gain' => 'integer',
        'elevation_loss' => 'integer',
        'max_elevation' => 'integer',
        'min_elevation' => 'integer',
        'estimated_duration' => 'integer',
        'view_count' => 'integer',
        'completion_count' => 'integer',
        'average_rating' => 'decimal:2',
        'review_count' => 'integer',
        'favorite_count' => 'integer',
    ];

    /**
     * 路线分类
     */
    public function category()
    {
        return $this->belongsTo(RouteCategory::class);
    }

    /**
     * 路线创建者
     */
    public function creator()
    {
        return $this->belongsTo(User::class, 'created_by');
    }

    /**
     * 路线验证者
     */
    public function verifier()
    {
        return $this->belongsTo(User::class, 'verified_by');
    }

    /**
     * 路线关键点
     */
    public function points()
    {
        return $this->hasMany(RoutePoint::class, 'route_id')->orderBy('sort_order');
    }

    /**
     * 路线评价
     */
    public function reviews()
    {
        return $this->hasMany(RouteReview::class);
    }

    /**
     * 路线照片
     */
    public function photos()
    {
        return $this->hasMany(RoutePhoto::class);
    }

    /**
     * 收藏此路线的用户
     */
    public function favoritedBy()
    {
        return $this->belongsToMany(User::class, 'route_favorites')
                    ->withTimestamps();
    }

    /**
     * 天气预警
     */
    public function weatherAlerts()
    {
        return $this->hasMany(WeatherAlert::class);
    }

    /**
     * 地质风险
     */
    public function geologicalRisks()
    {
        return $this->hasMany(GeologicalRisk::class);
    }

    /**
     * 用户活动记录
     */
    public function activities()
    {
        return $this->hasMany(UserActivity::class);
    }

    /**
     * 获取路线封面图
     */
    public function getCoverImageAttribute()
    {
        return $this->getFirstMediaUrl('cover') ?: asset('images/default-route-cover.jpg');
    }

    /**
     * 获取路线图片集
     */
    public function getGalleryImagesAttribute()
    {
        return $this->getMedia('gallery')->map(function (Media $media) {
            return [
                'id' => $media->id,
                'url' => $media->getUrl(),
                'thumb' => $media->getUrl('thumb'),
                'alt' => $media->getCustomProperty('alt', $this->name),
            ];
        });
    }

    /**
     * 获取难度等级中文名称
     */
    public function getDifficultyLevelNameAttribute()
    {
        $levels = [
            'easy' => '简单',
            'moderate' => '中等',
            'hard' => '困难',
            'extreme' => '极限',
        ];

        return $levels[$this->difficulty_level] ?? '未知';
    }

    /**
     * 获取技术等级描述
     */
    public function getTechnicalGradeDescriptionAttribute()
    {
        $grades = [
            'T1' => '简单徒步，路径清晰',
            'T2' => '山地徒步，需要基本导航',
            'T3' => '要求徒步经验，可能需要使用手脚并用',
            'T4' => '高山徒步，需要攀爬技能',
            'T5' => '技术性攀登，需要绳索保护',
            'T6' => '极限攀登，需要专业技术装备',
        ];

        return $grades[$this->technical_grade] ?? '未定义';
    }

    /**
     * 获取当前安全状态
     */
    public function getCurrentSafetyStatusAttribute()
    {
        $activeAlerts = $this->weatherAlerts()
            ->where('status', 'active')
            ->where('start_time', '<=', now())
            ->where('end_time', '>=', now())
            ->get();

        $highRisks = $this->geologicalRisks()
            ->where('status', 'active')
            ->whereIn('risk_level', ['high', 'extreme'])
            ->get();

        if ($activeAlerts->where('severity', 'extreme')->count() > 0 || 
            $highRisks->where('risk_level', 'extreme')->count() > 0) {
            return 'extreme_danger';
        }

        if ($activeAlerts->where('severity', 'high')->count() > 0 || 
            $highRisks->where('risk_level', 'high')->count() > 0) {
            return 'high_risk';
        }

        if ($activeAlerts->count() > 0 || $highRisks->count() > 0) {
            return 'moderate_risk';
        }

        return 'safe';
    }

    /**
     * 检查路线是否可以公开访问
     */
    public function isAccessible(): bool
    {
        return $this->is_public && 
               $this->status === 'approved' && 
               in_array($this->verification_status, ['community_verified', 'expert_verified', 'official_verified']);
    }

    /**
     * 增加浏览次数
     */
    public function incrementViewCount()
    {
        $this->increment('view_count');
    }

    /**
     * 增加完成次数
     */
    public function incrementCompletionCount()
    {
        $this->increment('completion_count');
    }

    /**
     * 更新平均评分
     */
    public function updateAverageRating()
    {
        $avgRating = $this->reviews()->avg('rating');
        $reviewCount = $this->reviews()->count();
        
        $this->update([
            'average_rating' => $avgRating ?: 0,
            'review_count' => $reviewCount,
        ]);
    }

    /**
     * 搜索范围查询
     */
    public function scopeNearby($query, $latitude, $longitude, $radius = 50)
    {
        return $query->whereRaw(
            "ST_Distance_Sphere(
                POINT(start_longitude, start_latitude),
                POINT(?, ?)
            ) <= ?",
            [$longitude, $latitude, $radius * 1000]
        );
    }

    /**
     * 按难度筛选
     */
    public function scopeByDifficulty($query, $difficulty)
    {
        return $query->where('difficulty_level', $difficulty);
    }

    /**
     * 按分类筛选
     */
    public function scopeByCategory($query, $categoryId)
    {
        return $query->where('category_id', $categoryId);
    }

    /**
     * 只显示已验证的路线
     */
    public function scopeVerified($query)
    {
        return $query->whereIn('verification_status', ['community_verified', 'expert_verified', 'official_verified']);
    }

    /**
     * 只显示公开的路线
     */
    public function scopePublic($query)
    {
        return $query->where('is_public', true)->where('status', 'approved');
    }
}
