# 户外徒步平台 (Outdoor Hiking Platform)

一个专业的户外徒步路线管理与安全预警系统，基于Laravel框架构建。

## 🌟 核心功能

### 1. 路线管理系统
- **标准化路线库**：统一的路线数据格式，包含详细的地理信息、难度评级、装备要求等
- **智能难度评估**：基于距离、爬升、海拔、技术等级的综合评分系统
- **多级验证机制**：社区验证、专家验证、官方验证的三级认证体系
- **GPX轨迹支持**：完整的GPX文件上传、解析和展示功能

### 2. 安全预警系统
- **实时天气预警**：集成权威天气API，提供分钟级天气预警
- **地质风险评估**：标注地质风险点，提供专业的安全建议
- **动态安全评级**：基于多维度数据的实时安全状态评估
- **应急响应机制**：紧急联系人、位置分享、应急报告功能

### 3. 用户社区功能
- **UGC内容生态**：用户贡献路线、照片、评价的完整生态
- **积分等级系统**：多维度积分奖励和用户等级体系
- **社交互动**：群组管理、活动组织、经验分享
- **个性化定制**：DIY仪表板、个性化推荐

### 4. 专业摄影功能
- **"人生照"照片墙**：高质量户外摄影作品展示
- **摄影教程指导**：构图、光影、滤镜等专业指导
- **作品评选活动**：定期摄影比赛和优秀作品展示

## 🏗️ 技术架构

### 后端技术栈
- **框架**：Laravel 10.x
- **数据库**：MySQL 8.0+
- **缓存**：Redis
- **队列**：Redis + Horizon
- **文件存储**：本地存储 / AWS S3
- **实时通信**：Pusher / Laravel Echo

### 核心组件
- **认证系统**：Laravel Sanctum
- **权限管理**：Spatie Laravel Permission
- **媒体处理**：Spatie Laravel MediaLibrary
- **图像处理**：Intervention Image
- **API资源**：Laravel API Resources

### 数据库设计

#### 核心数据表
```
users                 - 用户表
hiking_routes         - 徒步路线表
route_categories      - 路线分类表
route_points          - 路线关键点表
route_reviews         - 路线评价表
route_photos          - 路线照片表
weather_alerts        - 天气预警表
geological_risks      - 地质风险表
user_activities       - 用户活动记录表
groups               - 户外群组表
safety_reports       - 安全报告表
```

## 🚀 快速开始

### 环境要求
- PHP 8.1+
- MySQL 8.0+
- Redis 6.0+
- Composer 2.0+
- Node.js 16+ (用于前端资源编译)

### 安装步骤

1. **克隆项目**
```bash
git clone <repository-url>
cd outdoor-hiking-platform
```

2. **安装依赖**
```bash
composer install
npm install
```

3. **环境配置**
```bash
cp .env.example .env
php artisan key:generate
```

4. **配置数据库**
编辑 `.env` 文件，配置数据库连接：
```env
DB_CONNECTION=mysql
DB_HOST=127.0.0.1
DB_PORT=3306
DB_DATABASE=outdoor_hiking_platform
DB_USERNAME=your_username
DB_PASSWORD=your_password
```

5. **配置外部服务**
```env
# 天气API配置
WEATHER_API_KEY=your_openweather_api_key

# 地图API配置
BAIDU_MAP_API_KEY=your_baidu_map_key
AMAP_API_KEY=your_amap_key

# 微信小程序配置
WECHAT_MINI_PROGRAM_APPID=your_wechat_appid
WECHAT_MINI_PROGRAM_SECRET=your_wechat_secret
```

6. **数据库迁移**
```bash
php artisan migrate
php artisan db:seed
```

7. **创建存储链接**
```bash
php artisan storage:link
```

8. **启动服务**
```bash
# 启动Laravel开发服务器
php artisan serve

# 启动队列处理器
php artisan queue:work

# 启动Horizon (可选)
php artisan horizon
```

## 📱 API文档

### 认证相关
```
POST /api/v1/auth/register     - 用户注册
POST /api/v1/auth/login        - 用户登录
POST /api/v1/auth/logout       - 用户登出
GET  /api/v1/auth/user         - 获取当前用户信息
```

### 路线相关
```
GET    /api/v1/routes              - 获取路线列表
GET    /api/v1/routes/{slug}       - 获取路线详情
POST   /api/v1/routes              - 创建路线
PUT    /api/v1/routes/{id}         - 更新路线
DELETE /api/v1/routes/{id}         - 删除路线
GET    /api/v1/routes/nearby       - 获取附近路线
GET    /api/v1/routes/categories   - 获取路线分类
```

### 安全相关
```
GET  /api/v1/safety/alerts           - 获取安全预警
GET  /api/v1/weather/current         - 获取当前天气
GET  /api/v1/weather/forecast        - 获取天气预报
POST /api/v1/safety/reports          - 提交安全报告
```

### 用户相关
```
GET  /api/v1/user/profile       - 获取用户资料
PUT  /api/v1/user/profile       - 更新用户资料
GET  /api/v1/user/dashboard     - 获取用户仪表板
GET  /api/v1/user/favorites     - 获取收藏路线
GET  /api/v1/user/activities    - 获取活动记录
```

## 🔧 配置说明

### 路线难度配置
系统支持四个难度等级：
- **简单 (Easy)**：适合初学者，路径清晰，风险较低
- **中等 (Moderate)**：需要一定经验，有一定挑战性
- **困难 (Hard)**：需要丰富经验和良好体能
- **极限 (Extreme)**：专业级别，需要专业装备和技能

### 技术等级配置
采用国际标准的T1-T6技术等级：
- **T1**：简单徒步，路径清晰
- **T2**：山地徒步，需要基本导航
- **T3**：要求徒步经验，可能需要手脚并用
- **T4**：高山徒步，需要攀爬技能
- **T5**：技术性攀登，需要绳索保护
- **T6**：极限攀登，需要专业技术装备

### 安全预警配置
支持多种预警类型：
- **天气预警**：强降雨、雷暴、大风、极端温度等
- **地质风险**：落石、滑坡、雪崩、悬崖等
- **风险等级**：低、中、高、极高四个等级

## 🛡️ 安全特性

### 数据安全
- 用户密码加密存储
- API接口认证和授权
- 敏感数据脱敏处理
- SQL注入防护

### 业务安全
- 路线数据多级验证
- 实时安全状态监控
- 应急响应机制
- 位置隐私保护

## 🤝 贡献指南

1. Fork 项目
2. 创建功能分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 创建 Pull Request

## 📄 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情。

## 📞 联系我们

- 项目主页：[GitHub Repository]
- 问题反馈：[GitHub Issues]
- 邮箱：<EMAIL>

## 🙏 致谢

感谢所有为户外安全和徒步运动发展做出贡献的开发者和户外爱好者。

---

**注意**：这是一个开源项目，仅供学习和研究使用。在生产环境中使用前，请确保进行充分的测试和安全评估。
