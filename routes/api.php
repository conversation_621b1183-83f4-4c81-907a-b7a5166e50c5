<?php

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Route;
use App\Http\Controllers\Api\AuthController;
use App\Http\Controllers\Api\RouteController;
use App\Http\Controllers\Api\UserController;
use App\Http\Controllers\Api\SafetyController;
use App\Http\Controllers\Api\ReviewController;
use App\Http\Controllers\Api\PhotoController;
use App\Http\Controllers\Api\GroupController;
use App\Http\Controllers\Api\WeatherController;
use App\Http\Controllers\Api\NotificationController;

/*
|--------------------------------------------------------------------------
| API Routes
|--------------------------------------------------------------------------
|
| Here is where you can register API routes for your application. These
| routes are loaded by the RouteServiceProvider and all of them will
| be assigned to the "api" middleware group. Make something great!
|
*/

// 公开路由
Route::prefix('v1')->group(function () {
    
    // 认证相关
    Route::prefix('auth')->group(function () {
        Route::post('register', [AuthController::class, 'register']);
        Route::post('login', [AuthController::class, 'login']);
        Route::post('forgot-password', [AuthController::class, 'forgotPassword']);
        Route::post('reset-password', [AuthController::class, 'resetPassword']);
        Route::post('verify-email', [AuthController::class, 'verifyEmail']);
        Route::post('resend-verification', [AuthController::class, 'resendVerification']);
        
        // 微信小程序登录
        Route::post('wechat/login', [AuthController::class, 'wechatLogin']);
    });

    // 路线相关（公开）
    Route::prefix('routes')->group(function () {
        Route::get('/', [RouteController::class, 'index']);
        Route::get('/categories', [RouteController::class, 'categories']);
        Route::get('/nearby', [RouteController::class, 'nearby']);
        Route::get('/{route:slug}', [RouteController::class, 'show']);
        Route::get('/{route:slug}/photos', [PhotoController::class, 'routePhotos']);
        Route::get('/{route:slug}/reviews', [ReviewController::class, 'routeReviews']);
    });

    // 天气相关（公开）
    Route::prefix('weather')->group(function () {
        Route::get('/current', [WeatherController::class, 'current']);
        Route::get('/forecast', [WeatherController::class, 'forecast']);
        Route::get('/alerts', [WeatherController::class, 'alerts']);
    });

    // 安全信息（公开）
    Route::prefix('safety')->group(function () {
        Route::get('/alerts', [SafetyController::class, 'alerts']);
        Route::get('/geological-risks', [SafetyController::class, 'geologicalRisks']);
    });

    // 用户信息（公开）
    Route::prefix('users')->group(function () {
        Route::get('/{user:username}', [UserController::class, 'show']);
        Route::get('/{user:username}/routes', [UserController::class, 'userRoutes']);
        Route::get('/{user:username}/photos', [UserController::class, 'userPhotos']);
    });
});

// 需要认证的路由
Route::prefix('v1')->middleware('auth:sanctum')->group(function () {
    
    // 认证用户信息
    Route::prefix('auth')->group(function () {
        Route::get('user', [AuthController::class, 'user']);
        Route::post('logout', [AuthController::class, 'logout']);
        Route::post('refresh', [AuthController::class, 'refresh']);
    });

    // 用户相关
    Route::prefix('user')->group(function () {
        Route::get('profile', [UserController::class, 'profile']);
        Route::put('profile', [UserController::class, 'updateProfile']);
        Route::post('avatar', [UserController::class, 'uploadAvatar']);
        Route::get('dashboard', [UserController::class, 'dashboard']);
        Route::put('dashboard', [UserController::class, 'updateDashboard']);
        Route::get('activities', [UserController::class, 'activities']);
        Route::get('favorites', [UserController::class, 'favorites']);
        Route::get('statistics', [UserController::class, 'statistics']);
        
        // 安全设置
        Route::put('emergency-contacts', [UserController::class, 'updateEmergencyContacts']);
        Route::put('location-sharing', [UserController::class, 'updateLocationSharing']);
        Route::put('preferences', [UserController::class, 'updatePreferences']);
    });

    // 路线相关（需要认证）
    Route::prefix('routes')->group(function () {
        Route::post('/', [RouteController::class, 'store']);
        Route::put('/{route}', [RouteController::class, 'update']);
        Route::delete('/{route}', [RouteController::class, 'destroy']);
        Route::post('/{route}/favorite', [RouteController::class, 'toggleFavorite']);
        Route::post('/{route}/complete', [RouteController::class, 'markComplete']);
        Route::post('/{route}/verify', [RouteController::class, 'verify']);
        
        // 路线管理（管理员/专家）
        Route::middleware('role:admin|expert')->group(function () {
            Route::get('/pending', [RouteController::class, 'pending']);
            Route::post('/{route}/approve', [RouteController::class, 'approve']);
            Route::post('/{route}/reject', [RouteController::class, 'reject']);
        });
    });

    // 评价相关
    Route::prefix('reviews')->group(function () {
        Route::post('/', [ReviewController::class, 'store']);
        Route::put('/{review}', [ReviewController::class, 'update']);
        Route::delete('/{review}', [ReviewController::class, 'destroy']);
        Route::post('/{review}/like', [ReviewController::class, 'toggleLike']);
    });

    // 照片相关
    Route::prefix('photos')->group(function () {
        Route::post('/', [PhotoController::class, 'store']);
        Route::put('/{photo}', [PhotoController::class, 'update']);
        Route::delete('/{photo}', [PhotoController::class, 'destroy']);
        Route::post('/{photo}/like', [PhotoController::class, 'toggleLike']);
        Route::get('/my-photos', [PhotoController::class, 'myPhotos']);
        
        // 照片墙
        Route::get('/wall', [PhotoController::class, 'photoWall']);
        Route::get('/featured', [PhotoController::class, 'featured']);
    });

    // 群组相关
    Route::prefix('groups')->group(function () {
        Route::get('/', [GroupController::class, 'index']);
        Route::post('/', [GroupController::class, 'store']);
        Route::get('/{group}', [GroupController::class, 'show']);
        Route::put('/{group}', [GroupController::class, 'update']);
        Route::delete('/{group}', [GroupController::class, 'destroy']);
        
        // 群组成员管理
        Route::post('/{group}/join', [GroupController::class, 'join']);
        Route::post('/{group}/leave', [GroupController::class, 'leave']);
        Route::post('/{group}/invite', [GroupController::class, 'invite']);
        Route::post('/{group}/kick/{user}', [GroupController::class, 'kick']);
        Route::put('/{group}/members/{user}/role', [GroupController::class, 'updateMemberRole']);
        
        // 群组活动
        Route::get('/{group}/activities', [GroupController::class, 'activities']);
        Route::post('/{group}/activities', [GroupController::class, 'createActivity']);
    });

    // 安全相关
    Route::prefix('safety')->group(function () {
        Route::post('/reports', [SafetyController::class, 'createReport']);
        Route::get('/my-reports', [SafetyController::class, 'myReports']);
        Route::post('/emergency', [SafetyController::class, 'emergency']);
        
        // 位置分享
        Route::post('/location', [SafetyController::class, 'updateLocation']);
        Route::get('/location/followers', [SafetyController::class, 'followers']);
        Route::get('/location/following', [SafetyController::class, 'following']);
    });

    // 通知相关
    Route::prefix('notifications')->group(function () {
        Route::get('/', [NotificationController::class, 'index']);
        Route::put('/{notification}/read', [NotificationController::class, 'markAsRead']);
        Route::put('/read-all', [NotificationController::class, 'markAllAsRead']);
        Route::delete('/{notification}', [NotificationController::class, 'destroy']);
        Route::get('/unread-count', [NotificationController::class, 'unreadCount']);
    });

    // 搜索相关
    Route::prefix('search')->group(function () {
        Route::get('/routes', [RouteController::class, 'search']);
        Route::get('/users', [UserController::class, 'search']);
        Route::get('/groups', [GroupController::class, 'search']);
        Route::get('/suggestions', [RouteController::class, 'searchSuggestions']);
    });

    // 推荐相关
    Route::prefix('recommendations')->group(function () {
        Route::get('/routes', [RouteController::class, 'recommended']);
        Route::get('/users', [UserController::class, 'recommended']);
        Route::get('/groups', [GroupController::class, 'recommended']);
    });
});

// 管理员路由
Route::prefix('v1/admin')->middleware(['auth:sanctum', 'role:admin'])->group(function () {
    
    // 用户管理
    Route::prefix('users')->group(function () {
        Route::get('/', [UserController::class, 'adminIndex']);
        Route::put('/{user}/status', [UserController::class, 'updateStatus']);
        Route::put('/{user}/role', [UserController::class, 'updateRole']);
        Route::get('/{user}/activities', [UserController::class, 'userActivities']);
    });

    // 路线管理
    Route::prefix('routes')->group(function () {
        Route::get('/', [RouteController::class, 'adminIndex']);
        Route::get('/statistics', [RouteController::class, 'statistics']);
        Route::post('/{route}/feature', [RouteController::class, 'toggleFeatured']);
    });

    // 安全管理
    Route::prefix('safety')->group(function () {
        Route::get('/reports', [SafetyController::class, 'adminReports']);
        Route::put('/reports/{report}/status', [SafetyController::class, 'updateReportStatus']);
        Route::post('/alerts', [SafetyController::class, 'createAlert']);
        Route::put('/alerts/{alert}', [SafetyController::class, 'updateAlert']);
    });

    // 系统统计
    Route::prefix('statistics')->group(function () {
        Route::get('/overview', [UserController::class, 'systemOverview']);
        Route::get('/users', [UserController::class, 'userStatistics']);
        Route::get('/routes', [RouteController::class, 'routeStatistics']);
        Route::get('/safety', [SafetyController::class, 'safetyStatistics']);
    });
});

// 健康检查
Route::get('health', function () {
    return response()->json([
        'status' => 'ok',
        'timestamp' => now()->toISOString(),
        'version' => config('app.version', '1.0.0'),
    ]);
});

// 获取当前认证用户信息（兼容性路由）
Route::middleware('auth:sanctum')->get('/user', function (Request $request) {
    return $request->user();
});
