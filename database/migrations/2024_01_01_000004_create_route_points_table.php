<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('route_points', function (Blueprint $table) {
            $table->id();
            $table->foreignId('route_id')->constrained('hiking_routes')->onDelete('cascade');
            $table->string('name'); // 点位名称
            $table->enum('type', [
                'start', 'end', 'waypoint', 'rest_point', 'water_source', 
                'camping_site', 'viewpoint', 'danger_zone', 'supply_point',
                'emergency_exit', 'shelter', 'toilet', 'parking'
            ]);
            
            // 地理位置
            $table->decimal('latitude', 10, 8);
            $table->decimal('longitude', 11, 8);
            $table->integer('elevation')->nullable(); // 海拔
            
            // 描述信息
            $table->text('description')->nullable();
            $table->text('notes')->nullable(); // 注意事项
            $table->json('facilities')->nullable(); // 设施信息
            $table->json('services')->nullable(); // 服务信息
            
            // 距离和时间
            $table->decimal('distance_from_start', 8, 2)->nullable(); // 距起点距离
            $table->integer('estimated_time_from_start')->nullable(); // 距起点预计时间(分钟)
            
            // 安全信息
            $table->enum('safety_level', ['safe', 'caution', 'danger'])->default('safe');
            $table->text('safety_notes')->nullable();
            $table->boolean('has_mobile_signal')->default(false);
            
            // 状态
            $table->boolean('is_active')->default(true);
            $table->integer('sort_order')->default(0);
            
            // 用户贡献
            $table->foreignId('created_by')->constrained('users');
            $table->boolean('is_verified')->default(false);
            $table->timestamp('last_verified_at')->nullable();
            
            $table->timestamps();
            
            // 索引
            $table->index(['route_id', 'type']);
            $table->index(['route_id', 'sort_order']);
            $table->spatialIndex(['latitude', 'longitude']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('route_points');
    }
};
