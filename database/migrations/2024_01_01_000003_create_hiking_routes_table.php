<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('hiking_routes', function (Blueprint $table) {
            $table->id();
            $table->string('name'); // 路线名称
            $table->string('slug')->unique();
            $table->text('description');
            $table->foreignId('category_id')->constrained('route_categories');
            $table->foreignId('created_by')->constrained('users');
            
            // 地理信息
            $table->decimal('start_latitude', 10, 8);
            $table->decimal('start_longitude', 11, 8);
            $table->decimal('end_latitude', 10, 8);
            $table->decimal('end_longitude', 11, 8);
            $table->string('province'); // 省份
            $table->string('city'); // 城市
            $table->string('district')->nullable(); // 区县
            $table->text('detailed_location'); // 详细位置描述
            
            // 路线数据
            $table->decimal('distance', 8, 2); // 总距离(公里)
            $table->integer('elevation_gain'); // 累计爬升(米)
            $table->integer('elevation_loss'); // 累计下降(米)
            $table->integer('max_elevation'); // 最高海拔(米)
            $table->integer('min_elevation'); // 最低海拔(米)
            $table->integer('estimated_duration'); // 预计时长(分钟)
            
            // 难度和等级
            $table->enum('difficulty_level', ['easy', 'moderate', 'hard', 'extreme']);
            $table->enum('technical_grade', ['T1', 'T2', 'T3', 'T4', 'T5', 'T6']); // 技术等级
            $table->decimal('difficulty_score', 3, 1)->nullable(); // 综合难度评分
            
            // 季节和时间
            $table->json('best_seasons'); // 最佳季节 ["spring", "summer", "autumn", "winter"]
            $table->time('recommended_start_time')->nullable();
            $table->time('latest_return_time')->nullable();
            
            // GPX和轨迹数据
            $table->text('gpx_file_path')->nullable();
            $table->longText('track_points')->nullable(); // JSON格式的轨迹点
            $table->json('waypoints')->nullable(); // 关键点位
            
            // 安全和风险
            $table->json('risk_factors')->nullable(); // 风险因素
            $table->text('safety_notes')->nullable(); // 安全注意事项
            $table->boolean('requires_permit')->default(false); // 是否需要许可证
            $table->text('permit_info')->nullable(); // 许可证信息
            
            // 装备和补给
            $table->json('required_equipment')->nullable(); // 必需装备
            $table->json('recommended_equipment')->nullable(); // 推荐装备
            $table->boolean('has_water_sources')->default(false); // 是否有水源
            $table->boolean('has_camping_sites')->default(false); // 是否有露营点
            $table->text('supply_info')->nullable(); // 补给信息
            
            // 状态和审核
            $table->enum('status', ['draft', 'pending', 'approved', 'rejected', 'archived'])->default('draft');
            $table->enum('verification_status', ['unverified', 'community_verified', 'expert_verified', 'official_verified'])->default('unverified');
            $table->foreignId('verified_by')->nullable()->constrained('users');
            $table->timestamp('verified_at')->nullable();
            $table->text('rejection_reason')->nullable();
            
            // 统计数据
            $table->integer('view_count')->default(0);
            $table->integer('completion_count')->default(0);
            $table->decimal('average_rating', 3, 2)->default(0);
            $table->integer('review_count')->default(0);
            $table->integer('favorite_count')->default(0);
            
            // 元数据
            $table->json('tags')->nullable(); // 标签
            $table->json('metadata')->nullable(); // 其他元数据
            $table->boolean('is_featured')->default(false); // 是否为精选路线
            $table->boolean('is_public')->default(true); // 是否公开
            
            $table->timestamps();
            
            // 索引
            $table->index(['category_id', 'difficulty_level']);
            $table->index(['province', 'city']);
            $table->index(['status', 'verification_status']);
            $table->index(['is_featured', 'is_public']);
            $table->index(['average_rating', 'review_count']);
            $table->spatialIndex(['start_latitude', 'start_longitude']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('hiking_routes');
    }
};
