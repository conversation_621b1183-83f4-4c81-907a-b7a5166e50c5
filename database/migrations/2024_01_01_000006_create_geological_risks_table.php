<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('geological_risks', function (Blueprint $table) {
            $table->id();
            $table->foreignId('route_id')->nullable()->constrained('hiking_routes')->onDelete('cascade');
            
            // 地理位置
            $table->decimal('latitude', 10, 8);
            $table->decimal('longitude', 11, 8);
            $table->decimal('radius', 8, 2)->default(0.5); // 影响半径(公里)
            
            // 风险类型
            $table->enum('risk_type', [
                'rockfall', 'landslide', 'avalanche', 'crevasse', 'unstable_ground',
                'cliff_edge', 'loose_rock', 'scree_slope', 'river_crossing',
                'glacier_hazard', 'volcanic_activity', 'sinkhole', 'erosion'
            ]);
            $table->enum('risk_level', ['low', 'moderate', 'high', 'extreme']);
            
            // 描述信息
            $table->string('title');
            $table->text('description');
            $table->text('safety_measures')->nullable(); // 安全措施
            $table->text('alternative_routes')->nullable(); // 替代路线
            
            // 时间相关
            $table->json('seasonal_factors')->nullable(); // 季节性因素
            $table->json('weather_dependencies')->nullable(); // 天气依赖性
            $table->timestamp('last_assessment_date')->nullable(); // 最后评估日期
            $table->timestamp('next_assessment_due')->nullable(); // 下次评估到期日
            
            // 地质数据
            $table->string('rock_type')->nullable(); // 岩石类型
            $table->string('soil_type')->nullable(); // 土壤类型
            $table->decimal('slope_angle', 5, 2)->nullable(); // 坡度角度
            $table->string('drainage_condition')->nullable(); // 排水条件
            $table->json('geological_features')->nullable(); // 地质特征
            
            // 历史记录
            $table->json('incident_history')->nullable(); // 事故历史
            $table->timestamp('last_incident_date')->nullable(); // 最后事故日期
            $table->integer('incident_frequency')->default(0); // 事故频率(年)
            
            // 监测数据
            $table->boolean('has_monitoring')->default(false); // 是否有监测设备
            $table->json('monitoring_data')->nullable(); // 监测数据
            $table->timestamp('last_monitoring_update')->nullable();
            
            // 数据源和验证
            $table->string('data_source'); // 数据来源
            $table->enum('verification_status', ['unverified', 'field_verified', 'expert_verified', 'official_verified'])->default('unverified');
            $table->foreignId('verified_by')->nullable()->constrained('users');
            $table->timestamp('verified_at')->nullable();
            
            // 状态
            $table->enum('status', ['active', 'monitoring', 'resolved', 'archived'])->default('active');
            $table->boolean('is_public')->default(true);
            $table->foreignId('created_by')->constrained('users');
            
            $table->timestamps();
            
            // 索引
            $table->index(['route_id', 'risk_level']);
            $table->index(['risk_type', 'status']);
            $table->index(['verification_status', 'is_public']);
            $table->spatialIndex(['latitude', 'longitude']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('geological_risks');
    }
};
