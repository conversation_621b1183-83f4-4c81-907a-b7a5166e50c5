<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('users', function (Blueprint $table) {
            $table->id();
            $table->string('username')->unique();
            $table->string('email')->unique();
            $table->timestamp('email_verified_at')->nullable();
            $table->string('password');
            $table->string('phone')->nullable();
            $table->string('real_name')->nullable();
            $table->enum('gender', ['male', 'female', 'other'])->nullable();
            $table->date('birth_date')->nullable();
            $table->string('avatar')->nullable();
            $table->text('bio')->nullable();
            
            // 户外经验相关
            $table->enum('hiking_level', ['beginner', 'intermediate', 'advanced', 'expert'])->default('beginner');
            $table->integer('hiking_years')->default(0);
            $table->json('certifications')->nullable(); // 户外认证证书
            $table->boolean('is_guide')->default(false); // 是否为领队
            $table->decimal('guide_rating', 3, 2)->nullable(); // 领队评分
            
            // 积分和等级
            $table->integer('points')->default(0);
            $table->string('level')->default('新手');
            $table->json('badges')->nullable(); // 徽章集合
            
            // 安全设置
            $table->boolean('emergency_contact_enabled')->default(false);
            $table->json('emergency_contacts')->nullable();
            $table->boolean('location_sharing_enabled')->default(false);
            
            // 个性化设置
            $table->json('preferences')->nullable(); // 用户偏好设置
            $table->json('custom_dashboard')->nullable(); // 自定义仪表板配置
            
            // 状态
            $table->enum('status', ['active', 'inactive', 'banned'])->default('active');
            $table->timestamp('last_active_at')->nullable();
            $table->rememberToken();
            $table->timestamps();
            
            // 索引
            $table->index(['hiking_level', 'is_guide']);
            $table->index(['status', 'last_active_at']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('users');
    }
};
